import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { OrderStatus, PaymentInfo, RecipientAddress } from '../entities/order.entity';

export class OrderLineItemResponseDto {
  @ApiProperty({
    description: 'Line item ID internal system',
    example: 1,
  })
  id: number;

  @ApiPropertyOptional({
    description: 'Line item ID from TikTok Shop',
    example: '7891234567890123456',
  })
  idTT?: string;

  @ApiPropertyOptional({
    description: 'Product ID from TikTok Shop',
    example: '7891234567890123456',
  })
  productIdTT?: string;

  @ApiPropertyOptional({
    description: 'SKU ID from TikTok Shop',
    example: '7891234567890123456',
  })
  skuIdTT?: string;

  @ApiPropertyOptional({
    description: 'Seller SKU',
    example: 'TSHIRT-BLK-M',
  })
  sellerSku?: string;

  @ApiPropertyOptional({
    description: 'Product name',
    example: 'Classic Cotton T-Shirt',
  })
  productName?: string;

  @ApiPropertyOptional({
    description: 'SKU name',
    example: 'Black, Medium',
  })
  skuName?: string;

  @ApiPropertyOptional({
    description: 'SKU image URL',
    example: 'https://example.com/image.jpg',
  })
  skuImage?: string;

  @ApiPropertyOptional({
    description: 'Original price',
    example: 29.99,
  })
  originalPrice?: number;

  @ApiPropertyOptional({
    description: 'Sale price',
    example: 24.99,
  })
  salePrice?: number;

  @ApiPropertyOptional({
    description: 'Currency',
    example: 'USD',
  })
  currency?: string;

  @ApiPropertyOptional({
    description: 'Platform discount',
    example: 2.00,
  })
  platformDiscount?: number;

  @ApiPropertyOptional({
    description: 'Seller discount',
    example: 3.00,
  })
  sellerDiscount?: number;

  @ApiPropertyOptional({
    description: 'Package ID',
    example: 'PKG123456',
  })
  packageId?: string;

  @ApiPropertyOptional({
    description: 'Package status',
    example: 'TO_FULFILL',
  })
  packageStatus?: string;

  @ApiPropertyOptional({
    description: 'Display status',
    example: 'AWAITING_SHIPMENT',
  })
  displayStatus?: string;

  @ApiPropertyOptional({
    description: 'Is dangerous good',
    example: false,
  })
  isDangerousGood?: boolean;

  @ApiPropertyOptional({
    description: 'Is gift',
    example: false,
  })
  isGift?: boolean;

  @ApiPropertyOptional({
    description: 'Quantity',
    example: 2,
  })
  quantity?: number;

  @ApiPropertyOptional({
    description: 'Retail delivery fee',
    example: 1.50,
  })
  retailDeliveryFee?: number;

  @ApiPropertyOptional({
    description: 'Buyer service fee',
    example: 0.50,
  })
  buyerServiceFee?: number;

  @ApiPropertyOptional({
    description: 'Small order fee',
    example: 1.00,
  })
  smallOrderFee?: number;

  @ApiPropertyOptional({
    description: 'Tracking number',
    example: '1Z999AA1234567890',
  })
  trackingNumber?: string;

  @ApiProperty({
    description: 'Created date',
    example: '2024-01-15T10:30:00Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Updated date',
    example: '2024-01-15T10:30:00Z',
  })
  updatedAt: Date;
}

export class TikTokShopResponseDto {
  @ApiProperty({
    description: 'TikTok Shop ID',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'TikTok Shop ID from TikTok',
    example: '7000714532876273420',
  })
  idTT: string;

  @ApiProperty({
    description: 'Shop name',
    example: 'My Beauty Store',
  })
  name: string;

  @ApiPropertyOptional({
    description: 'Shop region',
    example: 'US',
  })
  region?: string;

  code?: string;
  friendly_name?: string;
}

export class UserResponseDto {
  @ApiProperty({
    description: 'User ID',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'User email',
    example: '<EMAIL>',
  })
  email: string;

  @ApiPropertyOptional({
    description: 'User name',
    example: 'John Doe',
  })
  name?: string;
}

export class OrderResponseDto {
  @ApiProperty({
    description: 'Order ID internal system',
    example: 1,
  })
  id: number;

  @ApiPropertyOptional({
    description: 'Order ID from TikTok Shop',
    example: '7891234567890123456',
  })
  idTT?: string;

  @ApiPropertyOptional({
    description: 'Order status',
    enum: OrderStatus,
    example: OrderStatus.AWAITING_SHIPMENT,
  })
  status?: OrderStatus;

  @ApiPropertyOptional({
    description: 'Order type',
    example: 'PRE_ORDER',
  })
  orderType?: string;

  @ApiPropertyOptional({
    description: 'Create time from TikTok Shop',
    example: 1640995200,
  })
  createTimeTT?: number;

  @ApiPropertyOptional({
    description: 'Update time from TikTok Shop',
    example: 1640995200,
  })
  updateTimeTT?: number;

  @ApiPropertyOptional({
    description: 'Paid time from TikTok Shop',
    example: 1640995200,
  })
  paidTime?: number;

  @ApiPropertyOptional({
    description: 'Buyer email',
    example: 'buyer****@example.com',
  })
  buyerEmail?: string;

  @ApiPropertyOptional({
    description: 'Buyer message',
    example: 'Please deliver after 5 PM',
  })
  buyerMessage?: string;

  @ApiPropertyOptional({
    description: 'TikTok buyer user ID',
    example: '7123456789',
  })
  userIdTT?: string;

  @ApiPropertyOptional({
    description: 'Payment information',
    example: {
      totalAmount: '29.99',
      currency: 'USD',
      tax: '2.40',
      shippingFee: '5.99',
    },
  })
  payment?: PaymentInfo;

  @ApiPropertyOptional({
    description: 'Payment method name',
    example: 'Credit Card',
  })
  paymentMethodName?: string;

  @ApiPropertyOptional({
    description: 'Recipient address',
    example: {
      name: 'John Doe',
      addressLine1: '123 Main St',
      postalCode: '12345',
      regionCode: 'US',
    },
  })
  recipientAddress?: RecipientAddress;

  @ApiPropertyOptional({
    description: 'Fulfillment type',
    example: 'FULFILLMENT_BY_SELLER',
  })
  fulfillmentType?: string;

  @ApiPropertyOptional({
    description: 'Shipping provider',
    example: 'UPS',
  })
  shippingProvider?: string;

  @ApiPropertyOptional({
    description: 'Tracking number',
    example: '1Z999AA1234567890',
  })
  trackingNumber?: string;

  @ApiPropertyOptional({
    description: 'Delivery option name',
    example: 'Standard Shipping',
  })
  deliveryOptionName?: string;

  @ApiPropertyOptional({
    description: 'Is COD order',
    example: false,
  })
  isCod?: boolean;

  @ApiPropertyOptional({
    description: 'Is exchange order',
    example: false,
  })
  isExchangeOrder?: boolean;

  @ApiPropertyOptional({
    description: 'Is replacement order',
    example: false,
  })
  isReplacementOrder?: boolean;

  @ApiPropertyOptional({
    description: 'Is sample order',
    example: false,
  })
  isSampleOrder?: boolean;

  @ApiPropertyOptional({
    description: 'Packages information from TikTok Shop',
    example: [
      {
        id: '1154528977690660930'
      }
    ],
  })
  packages?: { id: string }[];

  @ApiPropertyOptional({
    description: 'Order line items',
    type: [OrderLineItemResponseDto],
  })
  lineItems?: OrderLineItemResponseDto[];

  @ApiPropertyOptional({
    description: 'TikTok Shop information',
    type: TikTokShopResponseDto,
  })
  tiktokShop?: TikTokShopResponseDto;

  @ApiPropertyOptional({
    description: 'User information',
    type: UserResponseDto,
  })
  user?: UserResponseDto;

  @ApiProperty({
    description: 'Created date',
    example: '2024-01-15T10:30:00Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Updated date',
    example: '2024-01-15T10:30:00Z',
  })
  updatedAt: Date;
}
